#!/usr/bin/env node
/**
 * www Meta Repo 单文件引导脚本
 *
 * 使用方式：
 *   node www.mjs
 *
 * 功能：
 * - 自动生成 pnpm-workspace.yaml（若不存在）
 * - 按内置清单 clone/pull 各子仓库
 * - 执行 pnpm install 建立 workspace 链接
 *
 * 修改仓库：编辑下方 repos 数组
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';

const root = process.cwd();

// 内置仓库清单
const repos = [
  { dir: 'mqbao', url: 'https://github.com/ysdj/mqbao.git' },
  { dir: 'dz-gm', url: 'https://github.com/ysdj/dz-gm.git' },
  { dir: 'yibi', url: 'https://github.com/ysdj/yibi.git' },
  { dir: 'podque', url: 'https://github.com/ysdj/podque.git' },
  { dir: 'dzhand', url: 'https://github.com/ysdj/dzhand.git' },
  { dir: 'txdh', url: 'https://github.com/ysdj/txdh.git' },
];

const wsFile = path.join(root, 'pnpm-workspace.yaml');

function run(cmd, args, cwd) {
  return new Promise((resolve, reject) => {
    const p = spawn(cmd, args, { cwd, stdio: 'inherit' });
    p.on('close', (code) => {
      if (code === 0) resolve();
      else reject(new Error(`${cmd} ${args.join(' ')} exit ${code}`));
    });
  });
}

async function ensureRepo({ dir, url }) {
  const abs = path.join(root, dir);
  if (!fs.existsSync(abs)) {
    console.log(`[www] cloning ${url} -> ${dir}`);
    await run('git', ['clone', url, dir], root);
  } else {
    console.log(`[www] updating ${dir}`);
    await run('git', ['pull', '--rebase'], abs).catch(() => {});
  }
}

async function main() {
  // 1) 自动生成 workspace 文件（若不存在）
  if (!fs.existsSync(wsFile)) {
    fs.writeFileSync(wsFile, "packages:\n  - '*'\n");
    console.log('[www] created pnpm-workspace.yaml');
  }

  // 2) 确保各子仓库存在并更新
  for (const item of repos) {
    if (!item?.dir || !item?.url) {
      console.warn('[www] skip invalid item:', item);
      continue;
    }
    await ensureRepo(item);
  }

  // 3) 安装 workspace 依赖
  console.log('[www] installing workspace deps ...');
  await run('pnpm', ['install'], root);
  console.log('[www] done.');
}

main().catch((e) => {
  console.error('[www] failed:', e);
  process.exit(1);
});

